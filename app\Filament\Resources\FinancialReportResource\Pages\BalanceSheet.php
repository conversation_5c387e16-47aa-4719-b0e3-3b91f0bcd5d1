<?php

namespace App\Filament\Resources\FinancialReportResource\Pages;

use App\Filament\Resources\FinancialReportResource;
use Filament\Resources\Pages\Page;

class BalanceSheet extends Page
{
    protected static string $resource = FinancialReportResource::class;
    protected static string $view = 'filament.resources.financial-report-resource.pages.balance-sheet';

    public function getBreadcrumbs(): array
    {
        return [
            url('/admin') => 'Beranda',
            url('/admin/financial-reports') => 'Manajemen Akuntansi',
            url('/admin/financial-reports') => 'Laporan Keuangan',
            '' => 'Neraca',
        ];
    }

    protected function getHeaderActions(): array
    {
        return [
            \Filament\Actions\Action::make('export_pdf')
                ->label('Export PDF')
                ->icon('heroicon-o-document-arrow-down')
                ->color('danger')
                ->action(fn () => $this->notify('success', 'Fitur export PDF akan segera tersedia')),
                
            \Filament\Actions\Action::make('export_excel')
                ->label('Export Excel')
                ->icon('heroicon-o-table-cells')
                ->color('success')
                ->action(fn () => $this->notify('success', 'Fitur export Excel akan segera tersedia')),
        ];
    }

    public function getBalanceSheetData(): array
    {
        // Simulasi data neraca
        return [
            'assets' => [
                'current_assets' => [
                    'name' => 'Aset Lancar',
                    'accounts' => [
                        ['name' => 'Kas', 'amount' => ********],
                        ['name' => 'Bank', 'amount' => ********],
                        ['name' => 'Piutang Dagang', 'amount' => ********],
                        ['name' => 'Persediaan', 'amount' => ********],
                    ],
                    'total' => *********
                ],
                'fixed_assets' => [
                    'name' => 'Aset Tetap',
                    'accounts' => [
                        ['name' => 'Tanah', 'amount' => *********],
                        ['name' => 'Bangunan', 'amount' => 1********],
                        ['name' => 'Kendaraan', 'amount' => ********],
                        ['name' => 'Peralatan', 'amount' => ********],
                    ],
                    'total' => 3********
                ],
                'total' => ********0
            ],
            'liabilities' => [
                'current_liabilities' => [
                    'name' => 'Kewajiban Lancar',
                    'accounts' => [
                        ['name' => 'Hutang Dagang', 'amount' => ********],
                        ['name' => 'Hutang Bank', 'amount' => ********],
                        ['name' => 'Hutang Pajak', 'amount' => 5000000],
                    ],
                    'total' => ********
                ],
                'long_term_liabilities' => [
                    'name' => 'Kewajiban Jangka Panjang',
                    'accounts' => [
                        ['name' => 'Hutang Bank Jangka Panjang', 'amount' => *********],
                    ],
                    'total' => *********
                ],
                'total' => ********0
            ],
            'equity' => [
                'accounts' => [
                    ['name' => 'Modal Saham', 'amount' => 2********],
                    ['name' => 'Laba Ditahan', 'amount' => ********],
                ],
                'total' => *********
            ]
        ];
    }
}
