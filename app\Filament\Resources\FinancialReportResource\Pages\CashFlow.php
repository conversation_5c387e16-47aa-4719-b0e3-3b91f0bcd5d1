<?php

namespace App\Filament\Resources\FinancialReportResource\Pages;

use App\Filament\Resources\FinancialReportResource;
use Filament\Resources\Pages\Page;

class CashFlow extends Page
{
    protected static string $resource = FinancialReportResource::class;
    protected static string $view = 'filament.resources.financial-report-resource.pages.cash-flow';

    public function getBreadcrumbs(): array
    {
        return [
            url('/admin') => 'Beranda',
            url('/admin/financial-reports') => 'Manajemen Akuntansi',
            url('/admin/financial-reports') => 'Laporan Keuangan',
            '' => 'Arus Kas',
        ];
    }

    protected function getHeaderActions(): array
    {
        return [
            \Filament\Actions\Action::make('export_pdf')
                ->label('Export PDF')
                ->icon('heroicon-o-document-arrow-down')
                ->color('danger')
                ->action(fn () => $this->notify('success', 'Fitur export PDF akan segera tersedia')),
                
            \Filament\Actions\Action::make('export_excel')
                ->label('Export Excel')
                ->icon('heroicon-o-table-cells')
                ->color('success')
                ->action(fn () => $this->notify('success', 'Fitur export Excel akan segera tersedia')),
        ];
    }

    public function getCashFlowData(): array
    {
        // Simulasi data arus kas
        return [
            'operating_activities' => [
                'name' => 'Aktivitas Operasi',
                'items' => [
                    ['name' => 'Penerimaan dari Pelanggan', 'amount' => 1********],
                    ['name' => 'Pembayaran kepada Pemasok', 'amount' => -60000000],
                    ['name' => 'Pembayaran Gaji', 'amount' => -********],
                    ['name' => 'Pembayaran Beban Operasi', 'amount' => -********],
                ],
                'total' => -5000000
            ],
            'investing_activities' => [
                'name' => 'Aktivitas Investasi',
                'items' => [
                    ['name' => 'Pembelian Peralatan', 'amount' => -********],
                    ['name' => 'Penjualan Aset', 'amount' => 5000000],
                ],
                'total' => -********
            ],
            'financing_activities' => [
                'name' => 'Aktivitas Pendanaan',
                'items' => [
                    ['name' => 'Penerimaan Pinjaman Bank', 'amount' => ********],
                    ['name' => 'Pembayaran Dividen', 'amount' => -5000000],
                ],
                'total' => ********
            ],
            'net_cash_flow' => 5000000,
            'beginning_cash' => ********,
            'ending_cash' => ********
        ];
    }
}
