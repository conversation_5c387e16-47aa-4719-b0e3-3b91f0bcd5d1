<?php

namespace App\Filament\Widgets;

use Filament\Widgets\Widget;

class RecentActivitiesWidget extends Widget
{
    protected static string $view = 'filament.widgets.recent-activities';
    protected static ?string $heading = 'Aktivitas Terbaru';
    protected static ?int $sort = 3;
    protected int | string | array $columnSpan = 1;

    public function getViewData(): array
    {
        return [
            'activities' => [
                [
                    'activity' => 'Penjualan Produk A',
                    'amount' => 'Rp 5.500.000',
                    'date' => now()->subDays(1)->format('d M Y'),
                    'status' => 'completed',
                    'type' => 'income',
                ],
                [
                    'activity' => 'Pembayaran Supplier',
                    'amount' => 'Rp 2.300.000',
                    'date' => now()->subDays(2)->format('d M Y'),
                    'status' => 'completed',
                    'type' => 'expense',
                ],
                [
                    'activity' => 'Penjualan Produk B',
                    'amount' => 'Rp 3.200.000',
                    'date' => now()->subDays(3)->format('d M Y'),
                    'status' => 'pending',
                    'type' => 'income',
                ],
                [
                    'activity' => 'Biaya Operasional',
                    'amount' => 'Rp 1.500.000',
                    'date' => now()->subDays(4)->format('d M Y'),
                    'status' => 'completed',
                    'type' => 'expense',
                ],
                [
                    'activity' => 'Penjualan Produk C',
                    'amount' => 'Rp 4.800.000',
                    'date' => now()->subDays(5)->format('d M Y'),
                    'status' => 'completed',
                    'type' => 'income',
                ],
            ],
        ];
    }
}
