<?php

namespace App\Filament\Resources;

use App\Filament\Resources\FinancialReportResource\Pages;
use Filament\Resources\Resource;

class FinancialReportResource extends Resource
{
    protected static ?string $model = null; // No specific model for reports
    protected static ?string $navigationIcon = 'heroicon-o-document-chart-bar';
    protected static ?string $navigationLabel = 'Laporan Keuangan';
    protected static ?string $modelLabel = 'Laporan';
    protected static ?string $pluralModelLabel = 'Laporan Keuangan';
    protected static ?string $navigationGroup = 'Manajemen Akuntansi';
    protected static ?int $navigationSort = 3;

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListFinancialReports::route('/'),
            'balance-sheet' => Pages\BalanceSheet::route('/balance-sheet'),
            'income-statement' => Pages\IncomeStatement::route('/income-statement'),
            'cash-flow' => Pages\CashFlow::route('/cash-flow'),
        ];
    }

    public static function canCreate(): bool
    {
        return false;
    }
}
