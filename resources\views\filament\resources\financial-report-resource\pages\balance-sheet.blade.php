<x-filament-panels::page>
    <div class="space-y-6">
        {{-- Header --}}
        <div class="bg-gradient-to-r from-blue-600 to-blue-800 rounded-lg shadow-lg p-6 text-white">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold mb-2">Neraca (Balance Sheet)</h1>
                    <p class="text-blue-100">Laporan <PERSON></p>
                    <p class="text-blue-200 text-sm mt-1">
                        Per {{ now()->format('d F Y') }}
                    </p>
                </div>
                <div class="hidden md:block">
                    <x-filament::icon icon="heroicon-o-scale" class="w-16 h-16 text-blue-200" />
                </div>
            </div>
        </div>

        {{-- Balance Sheet Content --}}
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            @php $data = $this->getBalanceSheetData(); @endphp
            
            {{-- Assets --}}
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700">
                <div class="p-6">
                    <h2 class="text-xl font-bold text-gray-900 dark:text-white mb-4 flex items-center">
                        <x-filament::icon icon="heroicon-o-building-office" class="w-5 h-5 mr-2 text-blue-600" />
                        ASET
                    </h2>
                    
                    {{-- Current Assets --}}
                    <div class="mb-6">
                        <h3 class="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-3">
                            {{ $data['assets']['current_assets']['name'] }}
                        </h3>
                        @foreach($data['assets']['current_assets']['accounts'] as $account)
                            <div class="flex justify-between py-2 border-b border-gray-100 dark:border-gray-700">
                                <span class="text-gray-700 dark:text-gray-300">{{ $account['name'] }}</span>
                                <span class="font-medium text-gray-900 dark:text-white">
                                    Rp {{ number_format($account['amount'], 0, ',', '.') }}
                                </span>
                            </div>
                        @endforeach
                        <div class="flex justify-between py-2 font-semibold text-blue-600 dark:text-blue-400 border-t-2 border-blue-200 mt-2">
                            <span>Total Aset Lancar</span>
                            <span>Rp {{ number_format($data['assets']['current_assets']['total'], 0, ',', '.') }}</span>
                        </div>
                    </div>

                    {{-- Fixed Assets --}}
                    <div class="mb-6">
                        <h3 class="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-3">
                            {{ $data['assets']['fixed_assets']['name'] }}
                        </h3>
                        @foreach($data['assets']['fixed_assets']['accounts'] as $account)
                            <div class="flex justify-between py-2 border-b border-gray-100 dark:border-gray-700">
                                <span class="text-gray-700 dark:text-gray-300">{{ $account['name'] }}</span>
                                <span class="font-medium text-gray-900 dark:text-white">
                                    Rp {{ number_format($account['amount'], 0, ',', '.') }}
                                </span>
                            </div>
                        @endforeach
                        <div class="flex justify-between py-2 font-semibold text-blue-600 dark:text-blue-400 border-t-2 border-blue-200 mt-2">
                            <span>Total Aset Tetap</span>
                            <span>Rp {{ number_format($data['assets']['fixed_assets']['total'], 0, ',', '.') }}</span>
                        </div>
                    </div>

                    {{-- Total Assets --}}
                    <div class="flex justify-between py-3 font-bold text-lg text-blue-800 dark:text-blue-300 border-t-4 border-blue-400 bg-blue-50 dark:bg-blue-900/20 px-4 rounded">
                        <span>TOTAL ASET</span>
                        <span>Rp {{ number_format($data['assets']['total'], 0, ',', '.') }}</span>
                    </div>
                </div>
            </div>

            {{-- Liabilities & Equity --}}
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700">
                <div class="p-6">
                    {{-- Liabilities --}}
                    <h2 class="text-xl font-bold text-gray-900 dark:text-white mb-4 flex items-center">
                        <x-filament::icon icon="heroicon-o-exclamation-triangle" class="w-5 h-5 mr-2 text-red-600" />
                        KEWAJIBAN
                    </h2>
                    
                    {{-- Current Liabilities --}}
                    <div class="mb-6">
                        <h3 class="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-3">
                            {{ $data['liabilities']['current_liabilities']['name'] }}
                        </h3>
                        @foreach($data['liabilities']['current_liabilities']['accounts'] as $account)
                            <div class="flex justify-between py-2 border-b border-gray-100 dark:border-gray-700">
                                <span class="text-gray-700 dark:text-gray-300">{{ $account['name'] }}</span>
                                <span class="font-medium text-gray-900 dark:text-white">
                                    Rp {{ number_format($account['amount'], 0, ',', '.') }}
                                </span>
                            </div>
                        @endforeach
                        <div class="flex justify-between py-2 font-semibold text-red-600 dark:text-red-400 border-t-2 border-red-200 mt-2">
                            <span>Total Kewajiban Lancar</span>
                            <span>Rp {{ number_format($data['liabilities']['current_liabilities']['total'], 0, ',', '.') }}</span>
                        </div>
                    </div>

                    {{-- Long Term Liabilities --}}
                    <div class="mb-6">
                        <h3 class="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-3">
                            {{ $data['liabilities']['long_term_liabilities']['name'] }}
                        </h3>
                        @foreach($data['liabilities']['long_term_liabilities']['accounts'] as $account)
                            <div class="flex justify-between py-2 border-b border-gray-100 dark:border-gray-700">
                                <span class="text-gray-700 dark:text-gray-300">{{ $account['name'] }}</span>
                                <span class="font-medium text-gray-900 dark:text-white">
                                    Rp {{ number_format($account['amount'], 0, ',', '.') }}
                                </span>
                            </div>
                        @endforeach
                        <div class="flex justify-between py-2 font-semibold text-red-600 dark:text-red-400 border-t-2 border-red-200 mt-2">
                            <span>Total Kewajiban Jangka Panjang</span>
                            <span>Rp {{ number_format($data['liabilities']['long_term_liabilities']['total'], 0, ',', '.') }}</span>
                        </div>
                    </div>

                    {{-- Total Liabilities --}}
                    <div class="flex justify-between py-2 font-bold text-red-700 dark:text-red-400 border-t-2 border-red-300 mb-6">
                        <span>TOTAL KEWAJIBAN</span>
                        <span>Rp {{ number_format($data['liabilities']['total'], 0, ',', '.') }}</span>
                    </div>

                    {{-- Equity --}}
                    <h2 class="text-xl font-bold text-gray-900 dark:text-white mb-4 flex items-center">
                        <x-filament::icon icon="heroicon-o-building-office-2" class="w-5 h-5 mr-2 text-green-600" />
                        EKUITAS
                    </h2>
                    
                    @foreach($data['equity']['accounts'] as $account)
                        <div class="flex justify-between py-2 border-b border-gray-100 dark:border-gray-700">
                            <span class="text-gray-700 dark:text-gray-300">{{ $account['name'] }}</span>
                            <span class="font-medium text-gray-900 dark:text-white">
                                Rp {{ number_format($account['amount'], 0, ',', '.') }}
                            </span>
                        </div>
                    @endforeach
                    
                    <div class="flex justify-between py-2 font-bold text-green-700 dark:text-green-400 border-t-2 border-green-300 mb-6">
                        <span>TOTAL EKUITAS</span>
                        <span>Rp {{ number_format($data['equity']['total'], 0, ',', '.') }}</span>
                    </div>

                    {{-- Total Liabilities & Equity --}}
                    <div class="flex justify-between py-3 font-bold text-lg text-gray-800 dark:text-gray-300 border-t-4 border-gray-400 bg-gray-50 dark:bg-gray-900/20 px-4 rounded">
                        <span>TOTAL KEWAJIBAN & EKUITAS</span>
                        <span>Rp {{ number_format($data['liabilities']['total'] + $data['equity']['total'], 0, ',', '.') }}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-filament-panels::page>
