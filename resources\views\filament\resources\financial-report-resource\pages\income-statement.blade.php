<x-filament-panels::page>
    <div class="space-y-6">
        {{-- Header --}}
        <div class="bg-gradient-to-r from-green-600 to-green-800 rounded-lg shadow-lg p-6 text-white">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold mb-2">Laporan Laba Rugi</h1>
                    <p class="text-green-100">Income Statement</p>
                    <p class="text-green-200 text-sm mt-1">
                        Periode: {{ now()->startOfMonth()->format('d F') }} - {{ now()->format('d F Y') }}
                    </p>
                </div>
                <div class="hidden md:block">
                    <x-filament::icon icon="heroicon-o-chart-bar" class="w-16 h-16 text-green-200" />
                </div>
            </div>
        </div>

        {{-- Income Statement Content --}}
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700">
            <div class="p-6">
                @php $data = $this->getIncomeStatementData(); @endphp
                
                {{-- Revenue Section --}}
                <div class="mb-8">
                    <h2 class="text-xl font-bold text-gray-900 dark:text-white mb-4 flex items-center">
                        <x-filament::icon icon="heroicon-o-arrow-trending-up" class="w-5 h-5 mr-2 text-green-600" />
                        {{ $data['revenue']['name'] }}
                    </h2>
                    
                    @foreach($data['revenue']['accounts'] as $account)
                        <div class="flex justify-between py-3 border-b border-gray-100 dark:border-gray-700">
                            <span class="text-gray-700 dark:text-gray-300 text-lg">{{ $account['name'] }}</span>
                            <span class="font-medium text-gray-900 dark:text-white text-lg">
                                Rp {{ number_format($account['amount'], 0, ',', '.') }}
                            </span>
                        </div>
                    @endforeach
                    
                    <div class="flex justify-between py-4 font-bold text-xl text-green-700 dark:text-green-400 border-t-2 border-green-300 bg-green-50 dark:bg-green-900/20 px-4 rounded mt-4">
                        <span>Total Pendapatan</span>
                        <span>Rp {{ number_format($data['revenue']['total'], 0, ',', '.') }}</span>
                    </div>
                </div>

                {{-- Expenses Section --}}
                <div class="mb-8">
                    <h2 class="text-xl font-bold text-gray-900 dark:text-white mb-4 flex items-center">
                        <x-filament::icon icon="heroicon-o-arrow-trending-down" class="w-5 h-5 mr-2 text-red-600" />
                        {{ $data['expenses']['name'] }}
                    </h2>
                    
                    @foreach($data['expenses']['accounts'] as $account)
                        <div class="flex justify-between py-3 border-b border-gray-100 dark:border-gray-700">
                            <span class="text-gray-700 dark:text-gray-300 text-lg">{{ $account['name'] }}</span>
                            <span class="font-medium text-gray-900 dark:text-white text-lg">
                                Rp {{ number_format($account['amount'], 0, ',', '.') }}
                            </span>
                        </div>
                    @endforeach
                    
                    <div class="flex justify-between py-4 font-bold text-xl text-red-700 dark:text-red-400 border-t-2 border-red-300 bg-red-50 dark:bg-red-900/20 px-4 rounded mt-4">
                        <span>Total Beban</span>
                        <span>Rp {{ number_format($data['expenses']['total'], 0, ',', '.') }}</span>
                    </div>
                </div>

                {{-- Net Income Section --}}
                <div class="border-t-4 border-gray-400 pt-6">
                    <div class="flex justify-between py-6 font-bold text-2xl {{ $data['net_income'] >= 0 ? 'text-green-700 dark:text-green-400 bg-green-50 dark:bg-green-900/20' : 'text-red-700 dark:text-red-400 bg-red-50 dark:bg-red-900/20' }} px-6 rounded-lg">
                        <span class="flex items-center">
                            @if($data['net_income'] >= 0)
                                <x-filament::icon icon="heroicon-o-face-smile" class="w-6 h-6 mr-2" />
                                LABA BERSIH
                            @else
                                <x-filament::icon icon="heroicon-o-face-frown" class="w-6 h-6 mr-2" />
                                RUGI BERSIH
                            @endif
                        </span>
                        <span>Rp {{ number_format($data['net_income'], 0, ',', '.') }}</span>
                    </div>
                </div>

                {{-- Summary Cards --}}
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mt-8">
                    <div class="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg text-center">
                        <div class="text-2xl font-bold text-green-600 dark:text-green-400">
                            {{ number_format(($data['revenue']['total'] / $data['revenue']['total']) * 100, 1) }}%
                        </div>
                        <div class="text-sm text-gray-600 dark:text-gray-400">Margin Pendapatan</div>
                    </div>
                    
                    <div class="bg-red-50 dark:bg-red-900/20 p-4 rounded-lg text-center">
                        <div class="text-2xl font-bold text-red-600 dark:text-red-400">
                            {{ number_format(($data['expenses']['total'] / $data['revenue']['total']) * 100, 1) }}%
                        </div>
                        <div class="text-sm text-gray-600 dark:text-gray-400">Rasio Beban</div>
                    </div>
                    
                    <div class="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg text-center">
                        <div class="text-2xl font-bold text-blue-600 dark:text-blue-400">
                            {{ number_format(($data['net_income'] / $data['revenue']['total']) * 100, 1) }}%
                        </div>
                        <div class="text-sm text-gray-600 dark:text-gray-400">Margin Laba</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-filament-panels::page>
