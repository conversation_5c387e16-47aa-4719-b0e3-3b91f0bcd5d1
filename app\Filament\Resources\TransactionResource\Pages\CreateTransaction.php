<?php

namespace App\Filament\Resources\TransactionResource\Pages;

use App\Filament\Resources\TransactionResource;
use Filament\Resources\Pages\CreateRecord;

class CreateTransaction extends CreateRecord
{
    protected static string $resource = TransactionResource::class;

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

    public function getBreadcrumbs(): array
    {
        return [
            url('/admin') => 'Beranda',
            url('/admin/transactions') => 'Manajemen Akuntansi',
            url('/admin/transactions') => 'Transaksi Keuangan',
            '' => 'Tambah Transaksi',
        ];
    }

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        // Calculate total amount from journal entries
        $totalDebit = collect($data['journalEntries'])->sum('debit');
        $totalCredit = collect($data['journalEntries'])->sum('credit');
        $data['total_amount'] = max($totalDebit, $totalCredit);
        
        return $data;
    }
}
