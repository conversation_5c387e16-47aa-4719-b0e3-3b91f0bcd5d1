<?php

namespace App\Filament\Resources\FinancialReportResource\Pages;

use App\Filament\Resources\FinancialReportResource;
use Filament\Resources\Pages\Page;

class ListFinancialReports extends Page
{
    protected static string $resource = FinancialReportResource::class;
    protected static string $view = 'filament.resources.financial-report-resource.pages.list-financial-reports';

    public function getBreadcrumbs(): array
    {
        return [
            url('/admin') => 'Beranda',
            url('/admin/financial-reports') => 'Manajemen Akuntansi',
            '' => 'Laporan Keuangan',
        ];
    }

    protected function getHeaderActions(): array
    {
        return [
            \Filament\Actions\Action::make('balance_sheet')
                ->label('Neraca')
                ->icon('heroicon-o-scale')
                ->color('primary')
                ->url(route('filament.admin.resources.financial-reports.balance-sheet')),
                
            \Filament\Actions\Action::make('income_statement')
                ->label('Laba Rugi')
                ->icon('heroicon-o-chart-bar')
                ->color('success')
                ->url(route('filament.admin.resources.financial-reports.income-statement')),
                
            \Filament\Actions\Action::make('cash_flow')
                ->label('Arus Kas')
                ->icon('heroicon-o-banknotes')
                ->color('warning')
                ->url(route('filament.admin.resources.financial-reports.cash-flow')),
        ];
    }
}
