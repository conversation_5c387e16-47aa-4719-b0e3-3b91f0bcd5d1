<x-filament-widgets::widget>
    <x-filament::section>
        <x-slot name="heading">
            Informasi Akuntansi <PERSON>
        </x-slot>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            @foreach($this->getViewData()['info_cards'] as $card)
                <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            @php
                                $colorClasses = [
                                    'primary' => 'text-blue-600 dark:text-blue-400',
                                    'success' => 'text-green-600 dark:text-green-400',
                                    'warning' => 'text-yellow-600 dark:text-yellow-400',
                                    'danger' => 'text-red-600 dark:text-red-400',
                                ];
                            @endphp
                            <x-filament::icon 
                                :icon="$card['icon']" 
                                class="w-8 h-8 {{ $colorClasses[$card['color']] ?? 'text-gray-600' }}"
                            />
                        </div>
                        <div class="ml-4 flex-1">
                            <h3 class="text-sm font-medium text-gray-900 dark:text-white">
                                {{ $card['title'] }}
                            </h3>
                            <p class="text-2xl font-bold text-gray-900 dark:text-white">
                                {{ $card['value'] }}
                            </p>
                            <p class="text-sm text-gray-500 dark:text-gray-400">
                                {{ $card['description'] }}
                            </p>
                        </div>
                    </div>
                </div>
            @endforeach
        </div>
    </x-filament::section>
</x-filament-widgets::widget>
