<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Transaction extends Model
{
    use HasFactory;

    protected $fillable = [
        'transaction_number',
        'date',
        'description',
        'reference',
        'total_amount',
        'status',
    ];

    protected $casts = [
        'date' => 'date',
        'total_amount' => 'decimal:2',
    ];

    public function journalEntries(): HasMany
    {
        return $this->hasMany(JournalEntry::class);
    }

    public static function getStatuses(): array
    {
        return [
            'draft' => 'Draft',
            'posted' => 'Posted',
            'cancelled' => 'Cancelled',
        ];
    }

    public function generateTransactionNumber(): string
    {
        $lastTransaction = self::whereDate('created_at', today())->latest()->first();
        $number = $lastTransaction ? (int) substr($lastTransaction->transaction_number, -4) + 1 : 1;
        
        return 'TRX-' . date('Ymd') . '-' . str_pad($number, 4, '0', STR_PAD_LEFT);
    }
}
