<x-filament-panels::page>
    <div class="space-y-6">
        {{-- Header --}}
        <div class="bg-gradient-to-r from-yellow-600 to-yellow-800 rounded-lg shadow-lg p-6 text-white">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold mb-2">Lapor<PERSON></h1>
                    <p class="text-yellow-100">Cash Flow Statement</p>
                    <p class="text-yellow-200 text-sm mt-1">
                        Periode: {{ now()->startOfMonth()->format('d F') }} - {{ now()->format('d F Y') }}
                    </p>
                </div>
                <div class="hidden md:block">
                    <x-filament::icon icon="heroicon-o-banknotes" class="w-16 h-16 text-yellow-200" />
                </div>
            </div>
        </div>

        {{-- Cash Flow Content --}}
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700">
            <div class="p-6">
                @php $data = $this->getCashFlowData(); @endphp
                
                {{-- Operating Activities --}}
                <div class="mb-8">
                    <h2 class="text-xl font-bold text-gray-900 dark:text-white mb-4 flex items-center">
                        <x-filament::icon icon="heroicon-o-cog-6-tooth" class="w-5 h-5 mr-2 text-blue-600" />
                        {{ $data['operating_activities']['name'] }}
                    </h2>
                    
                    @foreach($data['operating_activities']['items'] as $item)
                        <div class="flex justify-between py-3 border-b border-gray-100 dark:border-gray-700">
                            <span class="text-gray-700 dark:text-gray-300">{{ $item['name'] }}</span>
                            <span class="font-medium {{ $item['amount'] >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400' }}">
                                {{ $item['amount'] >= 0 ? '+' : '' }}Rp {{ number_format($item['amount'], 0, ',', '.') }}
                            </span>
                        </div>
                    @endforeach
                    
                    <div class="flex justify-between py-4 font-bold text-lg {{ $data['operating_activities']['total'] >= 0 ? 'text-green-700 dark:text-green-400 bg-green-50 dark:bg-green-900/20' : 'text-red-700 dark:text-red-400 bg-red-50 dark:bg-red-900/20' }} border-t-2 px-4 rounded mt-4">
                        <span>Kas Bersih dari Aktivitas Operasi</span>
                        <span>{{ $data['operating_activities']['total'] >= 0 ? '+' : '' }}Rp {{ number_format($data['operating_activities']['total'], 0, ',', '.') }}</span>
                    </div>
                </div>

                {{-- Investing Activities --}}
                <div class="mb-8">
                    <h2 class="text-xl font-bold text-gray-900 dark:text-white mb-4 flex items-center">
                        <x-filament::icon icon="heroicon-o-chart-pie" class="w-5 h-5 mr-2 text-purple-600" />
                        {{ $data['investing_activities']['name'] }}
                    </h2>
                    
                    @foreach($data['investing_activities']['items'] as $item)
                        <div class="flex justify-between py-3 border-b border-gray-100 dark:border-gray-700">
                            <span class="text-gray-700 dark:text-gray-300">{{ $item['name'] }}</span>
                            <span class="font-medium {{ $item['amount'] >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400' }}">
                                {{ $item['amount'] >= 0 ? '+' : '' }}Rp {{ number_format($item['amount'], 0, ',', '.') }}
                            </span>
                        </div>
                    @endforeach
                    
                    <div class="flex justify-between py-4 font-bold text-lg {{ $data['investing_activities']['total'] >= 0 ? 'text-green-700 dark:text-green-400 bg-green-50 dark:bg-green-900/20' : 'text-red-700 dark:text-red-400 bg-red-50 dark:bg-red-900/20' }} border-t-2 px-4 rounded mt-4">
                        <span>Kas Bersih dari Aktivitas Investasi</span>
                        <span>{{ $data['investing_activities']['total'] >= 0 ? '+' : '' }}Rp {{ number_format($data['investing_activities']['total'], 0, ',', '.') }}</span>
                    </div>
                </div>

                {{-- Financing Activities --}}
                <div class="mb-8">
                    <h2 class="text-xl font-bold text-gray-900 dark:text-white mb-4 flex items-center">
                        <x-filament::icon icon="heroicon-o-building-library" class="w-5 h-5 mr-2 text-green-600" />
                        {{ $data['financing_activities']['name'] }}
                    </h2>
                    
                    @foreach($data['financing_activities']['items'] as $item)
                        <div class="flex justify-between py-3 border-b border-gray-100 dark:border-gray-700">
                            <span class="text-gray-700 dark:text-gray-300">{{ $item['name'] }}</span>
                            <span class="font-medium {{ $item['amount'] >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400' }}">
                                {{ $item['amount'] >= 0 ? '+' : '' }}Rp {{ number_format($item['amount'], 0, ',', '.') }}
                            </span>
                        </div>
                    @endforeach
                    
                    <div class="flex justify-between py-4 font-bold text-lg {{ $data['financing_activities']['total'] >= 0 ? 'text-green-700 dark:text-green-400 bg-green-50 dark:bg-green-900/20' : 'text-red-700 dark:text-red-400 bg-red-50 dark:bg-red-900/20' }} border-t-2 px-4 rounded mt-4">
                        <span>Kas Bersih dari Aktivitas Pendanaan</span>
                        <span>{{ $data['financing_activities']['total'] >= 0 ? '+' : '' }}Rp {{ number_format($data['financing_activities']['total'], 0, ',', '.') }}</span>
                    </div>
                </div>

                {{-- Net Cash Flow Summary --}}
                <div class="border-t-4 border-gray-400 pt-6">
                    <div class="space-y-4">
                        <div class="flex justify-between py-3 font-bold text-xl {{ $data['net_cash_flow'] >= 0 ? 'text-green-700 dark:text-green-400' : 'text-red-700 dark:text-red-400' }}">
                            <span>Kenaikan (Penurunan) Bersih Kas</span>
                            <span>{{ $data['net_cash_flow'] >= 0 ? '+' : '' }}Rp {{ number_format($data['net_cash_flow'], 0, ',', '.') }}</span>
                        </div>
                        
                        <div class="flex justify-between py-3 text-lg text-gray-700 dark:text-gray-300">
                            <span>Kas Awal Periode</span>
                            <span>Rp {{ number_format($data['beginning_cash'], 0, ',', '.') }}</span>
                        </div>
                        
                        <div class="flex justify-between py-4 font-bold text-2xl text-blue-700 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20 px-6 rounded-lg border-t-2 border-blue-300">
                            <span class="flex items-center">
                                <x-filament::icon icon="heroicon-o-banknotes" class="w-6 h-6 mr-2" />
                                Kas Akhir Periode
                            </span>
                            <span>Rp {{ number_format($data['ending_cash'], 0, ',', '.') }}</span>
                        </div>
                    </div>
                </div>

                {{-- Summary Cards --}}
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mt-8">
                    <div class="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg text-center">
                        <div class="text-lg font-bold text-blue-600 dark:text-blue-400">
                            {{ $data['operating_activities']['total'] >= 0 ? '+' : '' }}Rp {{ number_format($data['operating_activities']['total'], 0, ',', '.') }}
                        </div>
                        <div class="text-sm text-gray-600 dark:text-gray-400">Operasi</div>
                    </div>
                    
                    <div class="bg-purple-50 dark:bg-purple-900/20 p-4 rounded-lg text-center">
                        <div class="text-lg font-bold text-purple-600 dark:text-purple-400">
                            {{ $data['investing_activities']['total'] >= 0 ? '+' : '' }}Rp {{ number_format($data['investing_activities']['total'], 0, ',', '.') }}
                        </div>
                        <div class="text-sm text-gray-600 dark:text-gray-400">Investasi</div>
                    </div>
                    
                    <div class="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg text-center">
                        <div class="text-lg font-bold text-green-600 dark:text-green-400">
                            {{ $data['financing_activities']['total'] >= 0 ? '+' : '' }}Rp {{ number_format($data['financing_activities']['total'], 0, ',', '.') }}
                        </div>
                        <div class="text-sm text-gray-600 dark:text-gray-400">Pendanaan</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-filament-panels::page>
