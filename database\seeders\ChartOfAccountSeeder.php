<?php

namespace Database\Seeders;

use App\Models\ChartOfAccount;
use Illuminate\Database\Seeder;

class ChartOfAccountSeeder extends Seeder
{
    public function run(): void
    {
        $accounts = [
            // ASET
            ['code' => '1000', 'name' => 'ASET', 'type' => 'asset', 'parent_id' => null],
            ['code' => '1100', 'name' => 'Aset Lancar', 'type' => 'asset', 'parent_code' => '1000'],
            ['code' => '1101', 'name' => 'Kas', 'type' => 'asset', 'parent_code' => '1100'],
            ['code' => '1102', 'name' => 'Bank', 'type' => 'asset', 'parent_code' => '1100'],
            ['code' => '1103', 'name' => 'Piutang Dagang', 'type' => 'asset', 'parent_code' => '1100'],
            ['code' => '1104', 'name' => 'Persediaan', 'type' => 'asset', 'parent_code' => '1100'],
            
            ['code' => '1200', 'name' => 'Aset Tetap', 'type' => 'asset', 'parent_code' => '1000'],
            ['code' => '1201', 'name' => 'Tanah', 'type' => 'asset', 'parent_code' => '1200'],
            ['code' => '1202', 'name' => 'Bangunan', 'type' => 'asset', 'parent_code' => '1200'],
            ['code' => '1203', 'name' => 'Kendaraan', 'type' => 'asset', 'parent_code' => '1200'],
            ['code' => '1204', 'name' => 'Peralatan', 'type' => 'asset', 'parent_code' => '1200'],

            // KEWAJIBAN
            ['code' => '2000', 'name' => 'KEWAJIBAN', 'type' => 'liability', 'parent_id' => null],
            ['code' => '2100', 'name' => 'Kewajiban Lancar', 'type' => 'liability', 'parent_code' => '2000'],
            ['code' => '2101', 'name' => 'Hutang Dagang', 'type' => 'liability', 'parent_code' => '2100'],
            ['code' => '2102', 'name' => 'Hutang Bank', 'type' => 'liability', 'parent_code' => '2100'],
            ['code' => '2103', 'name' => 'Hutang Pajak', 'type' => 'liability', 'parent_code' => '2100'],

            // EKUITAS
            ['code' => '3000', 'name' => 'EKUITAS', 'type' => 'equity', 'parent_id' => null],
            ['code' => '3101', 'name' => 'Modal Saham', 'type' => 'equity', 'parent_code' => '3000'],
            ['code' => '3102', 'name' => 'Laba Ditahan', 'type' => 'equity', 'parent_code' => '3000'],

            // PENDAPATAN
            ['code' => '4000', 'name' => 'PENDAPATAN', 'type' => 'revenue', 'parent_id' => null],
            ['code' => '4101', 'name' => 'Pendapatan Penjualan', 'type' => 'revenue', 'parent_code' => '4000'],
            ['code' => '4102', 'name' => 'Pendapatan Jasa', 'type' => 'revenue', 'parent_code' => '4000'],

            // BEBAN
            ['code' => '5000', 'name' => 'BEBAN', 'type' => 'expense', 'parent_id' => null],
            ['code' => '5101', 'name' => 'Beban Gaji', 'type' => 'expense', 'parent_code' => '5000'],
            ['code' => '5102', 'name' => 'Beban Sewa', 'type' => 'expense', 'parent_code' => '5000'],
            ['code' => '5103', 'name' => 'Beban Listrik', 'type' => 'expense', 'parent_code' => '5000'],
            ['code' => '5104', 'name' => 'Beban Telepon', 'type' => 'expense', 'parent_code' => '5000'],
        ];

        $createdAccounts = [];

        foreach ($accounts as $account) {
            $parentId = null;
            
            if (isset($account['parent_code'])) {
                $parent = $createdAccounts[$account['parent_code']] ?? null;
                $parentId = $parent ? $parent->id : null;
            } elseif (isset($account['parent_id'])) {
                $parentId = $account['parent_id'];
            }

            $createdAccount = ChartOfAccount::create([
                'code' => $account['code'],
                'name' => $account['name'],
                'type' => $account['type'],
                'parent_id' => $parentId,
                'description' => 'Akun ' . $account['name'],
                'is_active' => true,
            ]);

            $createdAccounts[$account['code']] = $createdAccount;
        }
    }
}
