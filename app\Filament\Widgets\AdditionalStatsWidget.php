<?php

namespace App\Filament\Widgets;

use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;

class AdditionalStatsWidget extends BaseWidget
{
    protected static ?int $sort = 5;
    protected int | string | array $columnSpan = 'full';

    protected function getStats(): array
    {
        return [
            Stat::make('Piutang Dagang', 'Rp 15.750.000')
                ->description('Total piutang yang belum dibayar')
                ->descriptionIcon('heroicon-m-clock')
                ->color('warning')
                ->chart([15, 14, 16, 15, 17, 16, 15]),

            Stat::make('Hutang Dagang', 'Rp 22.100.000')
                ->description('Total hutang yang harus dibayar')
                ->descriptionIcon('heroicon-m-exclamation-triangle')
                ->color('danger')
                ->chart([22, 21, 23, 22, 24, 23, 22]),

            Stat::make('Modal Usaha', 'Rp 250.000.000')
                ->description('Total modal perusahaan')
                ->descriptionIcon('heroicon-m-building-office')
                ->color('success')
                ->chart([250, 248, 252, 250, 255, 253, 250]),
        ];
    }

    protected function getColumns(): int
    {
        return 3;
    }
}
