<?php

namespace App\Filament\Widgets;

use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;

class AdditionalStatsWidget extends BaseWidget
{
    protected static ?int $sort = 5;
    protected int | string | array $columnSpan = 'full';
    
    protected function getStats(): array
    {
        return [
            Stat::make('Piutang Dagang', 'Rp 15.750.000')
                ->description('Total piutang yang belum dibayar')
                ->descriptionIcon('heroicon-m-clock')
                ->color('warning'),
                
            Stat::make('Hutang Dagang', 'Rp 22.100.000')
                ->description('Total hutang yang harus dibayar')
                ->descriptionIcon('heroicon-m-exclamation-triangle')
                ->color('danger'),
                
            Stat::make('Modal Usaha', 'Rp 250.000.000')
                ->description('Total modal perusahaan')
                ->descriptionIcon('heroicon-m-building-office')
                ->color('success'),
        ];
    }
    
    protected function getColumns(): int
    {
        return 3;
    }
}
