<?php

namespace App\Filament\Resources\FinancialReportResource\Pages;

use App\Filament\Resources\FinancialReportResource;
use Filament\Resources\Pages\Page;

class IncomeStatement extends Page
{
    protected static string $resource = FinancialReportResource::class;
    protected static string $view = 'filament.resources.financial-report-resource.pages.income-statement';

    public function getBreadcrumbs(): array
    {
        return [
            url('/admin') => 'Beranda',
            url('/admin/financial-reports') => 'Manajemen Akuntansi',
            url('/admin/financial-reports') => 'Laporan Keuangan',
            '' => 'Laba Rugi',
        ];
    }

    protected function getHeaderActions(): array
    {
        return [
            \Filament\Actions\Action::make('export_pdf')
                ->label('Export PDF')
                ->icon('heroicon-o-document-arrow-down')
                ->color('danger')
                ->action(fn () => $this->notify('success', 'Fitur export PDF akan segera tersedia')),
                
            \Filament\Actions\Action::make('export_excel')
                ->label('Export Excel')
                ->icon('heroicon-o-table-cells')
                ->color('success')
                ->action(fn () => $this->notify('success', 'Fitur export Excel akan segera tersedia')),
        ];
    }

    public function getIncomeStatementData(): array
    {
        // Simulasi data laba rugi
        return [
            'revenue' => [
                'name' => 'Pendapatan',
                'accounts' => [
                    ['name' => 'Pendapatan Penjualan', 'amount' => *********],
                    ['name' => 'Pendapatan Jasa', 'amount' => ********],
                ],
                'total' => 1********
            ],
            'expenses' => [
                'name' => 'Beban',
                'accounts' => [
                    ['name' => 'Beban Gaji', 'amount' => ********],
                    ['name' => 'Beban Sewa', 'amount' => ********],
                    ['name' => 'Beban Listrik', 'amount' => 8000000],
                    ['name' => 'Beban Telepon', 'amount' => 3000000],
                    ['name' => 'Beban Operasional Lainnya', 'amount' => ********],
                ],
                'total' => *********
            ],
            'net_income' => ********
        ];
    }
}
