<?php

namespace App\Filament\Pages;

use Filament\Pages\Dashboard as BaseDashboard;

class Dashboard extends BaseDashboard
{
    protected static ?string $navigationIcon = 'heroicon-o-home';
    protected static string $view = 'filament.pages.dashboard';
    protected static ?string $title = 'Dashboard SP Admin';

    public function getBreadcrumbs(): array
    {
        return [
            url('/admin') => 'Beranda',
            url('/admin') => 'Akuntansi',
            '' => 'Dashboard SP Admin',
        ];
    }

    public function getHeading(): string
    {
        return 'Dashboard SP Admin';
    }

    public function getSubheading(): ?string
    {
        return 'Sistem Informasi Akuntansi dan <PERSON>';
    }

    protected function getHeaderActions(): array
    {
        return [
            \Filament\Actions\Action::make('refresh')
                ->label('Refresh Data')
                ->icon('heroicon-o-arrow-path')
                ->action(fn () => $this->redirect(request()->header('Referer'))),

            \Filament\Actions\Action::make('export')
                ->label('Export Laporan')
                ->icon('heroicon-o-document-arrow-down')
                ->color('success')
                ->action(fn () => $this->notify('success', 'Fitur export akan segera tersedia')),
        ];
    }

    public function getWidgets(): array
    {
        return [
            \App\Filament\Widgets\AccountingStatsWidget::class,
            \App\Filament\Widgets\AccountingChartWidget::class,
            \App\Filament\Widgets\RecentActivitiesWidget::class,
            \App\Filament\Widgets\QuickInfoWidget::class,
        ];
    }

    public function getColumns(): int | string | array
    {
        return [
            'default' => 1,
            'sm' => 1,
            'md' => 2,
            'lg' => 3,
            'xl' => 3,
            '2xl' => 3,
        ];
    }
}
