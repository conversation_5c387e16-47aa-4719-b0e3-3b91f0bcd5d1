<?php

namespace App\Filament\Pages;

use Filament\Pages\Dashboard as BaseDashboard;

class Dashboard extends BaseDashboard
{
    protected static ?string $navigationIcon = 'heroicon-o-home';

    public function getWidgets(): array
    {
        return [
            \App\Filament\Widgets\AccountingStatsWidget::class,
            \App\Filament\Widgets\RevenueWidget::class,
            \App\Filament\Widgets\ExpenseWidget::class,
            \App\Filament\Widgets\ProfitWidget::class,
            \App\Filament\Widgets\AdditionalStatsWidget::class,
        ];
    }

    public function getColumns(): int | string | array
    {
        return [
            'default' => 1,
            'sm' => 2,
            'md' => 2,
            'lg' => 2,
            'xl' => 2,
            '2xl' => 2,
        ];
    }
}
