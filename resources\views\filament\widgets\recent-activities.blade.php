<x-filament-widgets::widget>
    <x-filament::section>
        <x-slot name="heading">
            Aktivitas Terbaru
        </x-slot>

        <div class="space-y-4">
            @foreach($this->getViewData()['activities'] as $activity)
                <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                    <div class="flex items-center space-x-3">
                        <div class="flex-shrink-0">
                            @if($activity['type'] === 'income')
                                <div class="w-8 h-8 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center">
                                    <x-filament::icon 
                                        icon="heroicon-o-arrow-trending-up" 
                                        class="w-4 h-4 text-green-600 dark:text-green-400"
                                    />
                                </div>
                            @else
                                <div class="w-8 h-8 bg-red-100 dark:bg-red-900 rounded-full flex items-center justify-center">
                                    <x-filament::icon 
                                        icon="heroicon-o-arrow-trending-down" 
                                        class="w-4 h-4 text-red-600 dark:text-red-400"
                                    />
                                </div>
                            @endif
                        </div>
                        <div>
                            <p class="text-sm font-medium text-gray-900 dark:text-white">
                                {{ $activity['activity'] }}
                            </p>
                            <p class="text-xs text-gray-500 dark:text-gray-400">
                                {{ $activity['date'] }}
                            </p>
                        </div>
                    </div>
                    <div class="text-right">
                        <p class="text-sm font-semibold {{ $activity['type'] === 'income' ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400' }}">
                            {{ $activity['type'] === 'income' ? '+' : '-' }}{{ $activity['amount'] }}
                        </p>
                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                            @if($activity['status'] === 'completed')
                                bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200
                            @elseif($activity['status'] === 'pending')
                                bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200
                            @else
                                bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200
                            @endif
                        ">
                            @if($activity['status'] === 'completed')
                                Selesai
                            @elseif($activity['status'] === 'pending')
                                Pending
                            @else
                                Gagal
                            @endif
                        </span>
                    </div>
                </div>
            @endforeach
        </div>
    </x-filament::section>
</x-filament-widgets::widget>
