<?php

namespace App\Filament\Resources\TransactionResource\Pages;

use App\Filament\Resources\TransactionResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditTransaction extends EditRecord
{
    protected static string $resource = TransactionResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

    public function getBreadcrumbs(): array
    {
        return [
            url('/admin') => 'Beranda',
            url('/admin/transactions') => 'Manajemen Akuntansi',
            url('/admin/transactions') => 'Transaksi <PERSON>uangan',
            '' => 'Edit Transaksi',
        ];
    }

    protected function mutateFormDataBeforeSave(array $data): array
    {
        // Calculate total amount from journal entries
        $totalDebit = collect($data['journalEntries'])->sum('debit');
        $totalCredit = collect($data['journalEntries'])->sum('credit');
        $data['total_amount'] = max($totalDebit, $totalCredit);
        
        return $data;
    }
}
