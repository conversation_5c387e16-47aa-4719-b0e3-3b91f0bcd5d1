[2025-08-08 14:47:01] local.ERROR: Class "App\Filament\Widgets\AdditionalStatsWidget" not found (View: C:\laragon\www\WB-PROJEK\WB-ACOUNTING-V1\vendor\filament\filament\resources\views\pages\dashboard.blade.php) {"userId":1,"exception":"[object] (Illuminate\\View\\ViewException(code: 0): Class \"App\\Filament\\Widgets\\AdditionalStatsWidget\" not found (View: C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\filament\\filament\\resources\\views\\pages\\dashboard.blade.php) at C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\filament\\filament\\src\\Pages\\Page.php:252)
[stacktrace]
#0 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(58): Illuminate\\View\\Engines\\CompilerEngine->handleViewException(Object(Error), 1)
#1 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(40): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->handleViewException(Object(Error), 1)
#2 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(76): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath('C:\\\\laragon\\\\www\\\\...', Array)
#3 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(16): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\laragon\\\\www\\\\...', Array)
#4 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get('C:\\\\laragon\\\\www\\\\...', Array)
#5 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#6 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#7 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(259): Illuminate\\View\\View->render(Object(Closure))
#8 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(303): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->Livewire\\Mechanisms\\HandleComponents\\{closure}()
#9 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(251): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->trackInRenderStack(Object(App\\Filament\\Pages\\Dashboard), Object(Closure))
#10 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(54): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->render(Object(App\\Filament\\Pages\\Dashboard), '<div></div>')
#11 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\livewire\\livewire\\src\\LivewireManager.php(73): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->mount('App\\\\Filament\\\\Pa...', Array, NULL)
#12 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\livewire\\livewire\\src\\Features\\SupportPageComponents\\HandlesPageComponents.php(17): Livewire\\LivewireManager->mount('App\\\\Filament\\\\Pa...', Array)
#13 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\livewire\\livewire\\src\\Features\\SupportPageComponents\\SupportPageComponents.php(117): Livewire\\Component->Livewire\\Features\\SupportPageComponents\\{closure}()
#14 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\livewire\\livewire\\src\\Features\\SupportPageComponents\\HandlesPageComponents.php(14): Livewire\\Features\\SupportPageComponents\\SupportPageComponents::interceptTheRenderOfTheComponentAndRetreiveTheLayoutConfiguration(Object(Closure))
#15 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): Livewire\\Component->__invoke()
#16 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Filament\\Pages\\Dashboard), '__invoke')
#17 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#18 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(822): Illuminate\\Routing\\Route->run()
#19 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\filament\\filament\\src\\Http\\Middleware\\DispatchServingFilamentEvent.php(15): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Filament\\Http\\Middleware\\DispatchServingFilamentEvent->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\filament\\filament\\src\\Http\\Middleware\\DisableBladeIconComponents.php(14): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Filament\\Http\\Middleware\\DisableBladeIconComponents->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\AuthenticateSession.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Session\\Middleware\\AuthenticateSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#36 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\filament\\filament\\src\\Http\\Middleware\\SetUpPanel.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Filament\\Http\\Middleware\\SetUpPanel->handle(Object(Illuminate\\Http\\Request), Object(Closure), Object(Filament\\Panel))
#43 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(821): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#45 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(800): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#46 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(764): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#47 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(753): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#48 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#49 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#59 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#61 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#62 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#63 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#64 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#65 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#66 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#67 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#68 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#69 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#70 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#71 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#72 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#73 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#74 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#75 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\laragon\\\\www\\\\...')
#76 {main}

[previous exception] [object] (Error(code: 0): Class \"App\\Filament\\Widgets\\AdditionalStatsWidget\" not found at C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\filament\\filament\\src\\Pages\\Page.php:252)
[stacktrace]
#0 [internal function]: Filament\\Pages\\Page->Filament\\Pages\\{closure}('App\\\\Filament\\\\Wi...')
#1 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\filament\\filament\\src\\Pages\\Page.php(252): array_filter(Array, Object(Closure))
#2 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\filament\\filament\\src\\Pages\\Dashboard.php(54): Filament\\Pages\\Page->filterVisibleWidgets(Array)
#3 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\storage\\framework\\views\\e15635894b5d540a56245d024b279dbc.php(23): Filament\\Pages\\Dashboard->getVisibleWidgets()
#4 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(37): include('C:\\\\laragon\\\\www\\\\...')
#5 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(38): App\\Filament\\Pages\\Dashboard->Livewire\\Mechanisms\\ExtendBlade\\{closure}()
#6 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(76): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath('C:\\\\laragon\\\\www\\\\...', Array)
#7 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(16): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\laragon\\\\www\\\\...', Array)
#8 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get('C:\\\\laragon\\\\www\\\\...', Array)
#9 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#10 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#11 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(259): Illuminate\\View\\View->render(Object(Closure))
#12 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(303): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->Livewire\\Mechanisms\\HandleComponents\\{closure}()
#13 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(251): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->trackInRenderStack(Object(App\\Filament\\Pages\\Dashboard), Object(Closure))
#14 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(54): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->render(Object(App\\Filament\\Pages\\Dashboard), '<div></div>')
#15 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\livewire\\livewire\\src\\LivewireManager.php(73): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->mount('App\\\\Filament\\\\Pa...', Array, NULL)
#16 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\livewire\\livewire\\src\\Features\\SupportPageComponents\\HandlesPageComponents.php(17): Livewire\\LivewireManager->mount('App\\\\Filament\\\\Pa...', Array)
#17 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\livewire\\livewire\\src\\Features\\SupportPageComponents\\SupportPageComponents.php(117): Livewire\\Component->Livewire\\Features\\SupportPageComponents\\{closure}()
#18 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\livewire\\livewire\\src\\Features\\SupportPageComponents\\HandlesPageComponents.php(14): Livewire\\Features\\SupportPageComponents\\SupportPageComponents::interceptTheRenderOfTheComponentAndRetreiveTheLayoutConfiguration(Object(Closure))
#19 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): Livewire\\Component->__invoke()
#20 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Filament\\Pages\\Dashboard), '__invoke')
#21 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#22 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(822): Illuminate\\Routing\\Route->run()
#23 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\filament\\filament\\src\\Http\\Middleware\\DispatchServingFilamentEvent.php(15): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Filament\\Http\\Middleware\\DispatchServingFilamentEvent->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\filament\\filament\\src\\Http\\Middleware\\DisableBladeIconComponents.php(14): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Filament\\Http\\Middleware\\DisableBladeIconComponents->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\AuthenticateSession.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Session\\Middleware\\AuthenticateSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#40 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\filament\\filament\\src\\Http\\Middleware\\SetUpPanel.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Filament\\Http\\Middleware\\SetUpPanel->handle(Object(Illuminate\\Http\\Request), Object(Closure), Object(Filament\\Panel))
#47 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(821): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#49 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(800): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#50 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(764): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#51 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(753): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#52 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#53 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#62 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#63 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#64 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#65 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#66 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#67 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#68 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#69 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#70 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#71 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#72 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#73 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#74 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#75 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#76 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#77 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#78 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#79 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\laragon\\\\www\\\\...')
#80 {main}
"} 
[2025-08-08 14:51:27] local.ERROR: Unable to find component: [app.filament.widgets.revenue-widget] {"userId":1,"exception":"[object] (Livewire\\Exceptions\\ComponentNotFoundException(code: 0): Unable to find component: [app.filament.widgets.revenue-widget] at C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\livewire\\livewire\\src\\Mechanisms\\ComponentRegistry.php:116)
[stacktrace]
#0 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\livewire\\livewire\\src\\Mechanisms\\ComponentRegistry.php(25): Livewire\\Mechanisms\\ComponentRegistry->getNameAndClass('app.filament.wi...')
#1 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\livewire\\livewire\\src\\LivewireManager.php(58): Livewire\\Mechanisms\\ComponentRegistry->new('app.filament.wi...', 'e9PvD4K2JLDtIhZ...')
#2 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(132): Livewire\\LivewireManager->new('app.filament.wi...', 'e9PvD4K2JLDtIhZ...')
#3 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(92): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->fromSnapshot(Array)
#4 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\livewire\\livewire\\src\\LivewireManager.php(102): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->update(Array, Array, Array)
#5 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleRequests\\HandleRequests.php(94): Livewire\\LivewireManager->update(Array, Array, Array)
#6 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): Livewire\\Mechanisms\\HandleRequests\\HandleRequests->handleUpdate()
#7 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(Livewire\\Mechanisms\\HandleRequests\\HandleRequests), 'handleUpdate')
#8 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#9 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(822): Illuminate\\Routing\\Route->run()
#10 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#11 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#12 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#13 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#14 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#15 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#19 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(821): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#26 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(800): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#27 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(764): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#28 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(753): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#29 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#30 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(47): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#51 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#52 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#53 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#54 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\laragon\\\\www\\\\...')
#55 {main}
"} 
[2025-08-08 14:53:27] local.ERROR: Method Illuminate\Support\Collection::toQuery does not exist. {"userId":1,"exception":"[object] (BadMethodCallException(code: 0): Method Illuminate\\Support\\Collection::toQuery does not exist. at C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Macroable\\Traits\\Macroable.php:115)
[stacktrace]
#0 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\app\\Filament\\Widgets\\RecentActivitiesWidget.php(86): Illuminate\\Support\\Collection->__call('toQuery', Array)
#1 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\app\\Filament\\Widgets\\RecentActivitiesWidget.php(19): App\\Filament\\Widgets\\RecentActivitiesWidget->getTableQuery()
#2 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\filament\\tables\\src\\Concerns\\InteractsWithTable.php(54): App\\Filament\\Widgets\\RecentActivitiesWidget->table(Object(Filament\\Tables\\Table))
#3 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\filament\\support\\src\\Components\\ComponentManager.php(80): Filament\\Widgets\\TableWidget->Filament\\Tables\\Concerns\\{closure}()
#4 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\filament\\support\\src\\Concerns\\Configurable.php(12): Filament\\Support\\Components\\ComponentManager->configureUsing('Filament\\\\Tables...', Object(Closure), Object(Closure), false)
#5 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\filament\\tables\\src\\Concerns\\InteractsWithTable.php(52): Filament\\Support\\Components\\Component::configureUsing(Object(Closure), Object(Closure))
#6 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\filament\\support\\src\\Components\\ComponentManager.php(80): Filament\\Widgets\\TableWidget->Filament\\Tables\\Concerns\\{closure}()
#7 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\filament\\support\\src\\Concerns\\Configurable.php(12): Filament\\Support\\Components\\ComponentManager->configureUsing('Filament\\\\Tables...', Object(Closure), Object(Closure), false)
#8 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\filament\\tables\\src\\Concerns\\InteractsWithTable.php(50): Filament\\Support\\Components\\Component::configureUsing(Object(Closure), Object(Closure))
#9 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Filament\\Widgets\\TableWidget->bootedInteractsWithTable()
#10 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#11 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#12 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#13 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\livewire\\livewire\\src\\Wrapped.php(23): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array)
#14 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\livewire\\livewire\\src\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks.php(144): Livewire\\Wrapped->__call('bootedInteracts...', Array)
#15 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\livewire\\livewire\\src\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks.php(24): Livewire\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks->callTraitHook('booted')
#16 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\livewire\\livewire\\src\\Features\\SupportLazyLoading\\SupportLazyLoading.php(175): Livewire\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks->mount(Array)
#17 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\livewire\\livewire\\src\\Features\\SupportLazyLoading\\SupportLazyLoading.php(100): Livewire\\Features\\SupportLazyLoading\\SupportLazyLoading->callMountLifecycleMethod(Array)
#18 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\livewire\\livewire\\src\\ComponentHook.php(41): Livewire\\Features\\SupportLazyLoading\\SupportLazyLoading->call('__lazyLoad', Array, Object(Closure))
#19 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\livewire\\livewire\\src\\ComponentHookRegistry.php(110): Livewire\\ComponentHook->callCall('__lazyLoad', Array, Object(Closure))
#20 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\livewire\\livewire\\src\\ComponentHookRegistry.php(65): Livewire\\ComponentHookRegistry::Livewire\\{closure}('__lazyLoad', Array, Object(Closure))
#21 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\livewire\\livewire\\src\\EventBus.php(60): Livewire\\ComponentHookRegistry::Livewire\\{closure}(Object(App\\Filament\\Widgets\\RecentActivitiesWidget), '__lazyLoad', Array, Object(Livewire\\Mechanisms\\HandleComponents\\ComponentContext), Object(Closure))
#22 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\livewire\\livewire\\src\\helpers.php(98): Livewire\\EventBus->trigger('call', Object(App\\Filament\\Widgets\\RecentActivitiesWidget), '__lazyLoad', Array, Object(Livewire\\Mechanisms\\HandleComponents\\ComponentContext), Object(Closure))
#23 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(471): Livewire\\trigger('call', Object(App\\Filament\\Widgets\\RecentActivitiesWidget), '__lazyLoad', Array, Object(Livewire\\Mechanisms\\HandleComponents\\ComponentContext), Object(Closure))
#24 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(101): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->callMethods(Object(App\\Filament\\Widgets\\RecentActivitiesWidget), Array, Object(Livewire\\Mechanisms\\HandleComponents\\ComponentContext))
#25 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\livewire\\livewire\\src\\LivewireManager.php(102): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->update(Array, Array, Array)
#26 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleRequests\\HandleRequests.php(94): Livewire\\LivewireManager->update(Array, Array, Array)
#27 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): Livewire\\Mechanisms\\HandleRequests\\HandleRequests->handleUpdate()
#28 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(Livewire\\Mechanisms\\HandleRequests\\HandleRequests), 'handleUpdate')
#29 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#30 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(822): Illuminate\\Routing\\Route->run()
#31 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#40 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(821): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#47 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(800): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#48 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(764): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#49 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(753): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#50 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#51 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(47): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#59 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#61 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#62 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#63 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#64 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#65 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#66 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#67 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#68 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#69 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#70 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#71 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#72 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#73 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#74 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#75 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\laragon\\\\www\\\\...')
#76 {main}
"} 
[2025-08-08 14:53:52] local.ERROR: Method Illuminate\Support\Collection::toQuery does not exist. {"userId":1,"exception":"[object] (BadMethodCallException(code: 0): Method Illuminate\\Support\\Collection::toQuery does not exist. at C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Macroable\\Traits\\Macroable.php:115)
[stacktrace]
#0 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\app\\Filament\\Widgets\\RecentActivitiesWidget.php(86): Illuminate\\Support\\Collection->__call('toQuery', Array)
#1 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\app\\Filament\\Widgets\\RecentActivitiesWidget.php(19): App\\Filament\\Widgets\\RecentActivitiesWidget->getTableQuery()
#2 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\filament\\tables\\src\\Concerns\\InteractsWithTable.php(54): App\\Filament\\Widgets\\RecentActivitiesWidget->table(Object(Filament\\Tables\\Table))
#3 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\filament\\support\\src\\Components\\ComponentManager.php(80): Filament\\Widgets\\TableWidget->Filament\\Tables\\Concerns\\{closure}()
#4 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\filament\\support\\src\\Concerns\\Configurable.php(12): Filament\\Support\\Components\\ComponentManager->configureUsing('Filament\\\\Tables...', Object(Closure), Object(Closure), false)
#5 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\filament\\tables\\src\\Concerns\\InteractsWithTable.php(52): Filament\\Support\\Components\\Component::configureUsing(Object(Closure), Object(Closure))
#6 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\filament\\support\\src\\Components\\ComponentManager.php(80): Filament\\Widgets\\TableWidget->Filament\\Tables\\Concerns\\{closure}()
#7 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\filament\\support\\src\\Concerns\\Configurable.php(12): Filament\\Support\\Components\\ComponentManager->configureUsing('Filament\\\\Tables...', Object(Closure), Object(Closure), false)
#8 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\filament\\tables\\src\\Concerns\\InteractsWithTable.php(50): Filament\\Support\\Components\\Component::configureUsing(Object(Closure), Object(Closure))
#9 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Filament\\Widgets\\TableWidget->bootedInteractsWithTable()
#10 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#11 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#12 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#13 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\livewire\\livewire\\src\\Wrapped.php(23): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array)
#14 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\livewire\\livewire\\src\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks.php(144): Livewire\\Wrapped->__call('bootedInteracts...', Array)
#15 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\livewire\\livewire\\src\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks.php(24): Livewire\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks->callTraitHook('booted')
#16 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\livewire\\livewire\\src\\Features\\SupportLazyLoading\\SupportLazyLoading.php(175): Livewire\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks->mount(Array)
#17 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\livewire\\livewire\\src\\Features\\SupportLazyLoading\\SupportLazyLoading.php(100): Livewire\\Features\\SupportLazyLoading\\SupportLazyLoading->callMountLifecycleMethod(Array)
#18 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\livewire\\livewire\\src\\ComponentHook.php(41): Livewire\\Features\\SupportLazyLoading\\SupportLazyLoading->call('__lazyLoad', Array, Object(Closure))
#19 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\livewire\\livewire\\src\\ComponentHookRegistry.php(110): Livewire\\ComponentHook->callCall('__lazyLoad', Array, Object(Closure))
#20 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\livewire\\livewire\\src\\ComponentHookRegistry.php(65): Livewire\\ComponentHookRegistry::Livewire\\{closure}('__lazyLoad', Array, Object(Closure))
#21 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\livewire\\livewire\\src\\EventBus.php(60): Livewire\\ComponentHookRegistry::Livewire\\{closure}(Object(App\\Filament\\Widgets\\RecentActivitiesWidget), '__lazyLoad', Array, Object(Livewire\\Mechanisms\\HandleComponents\\ComponentContext), Object(Closure))
#22 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\livewire\\livewire\\src\\helpers.php(98): Livewire\\EventBus->trigger('call', Object(App\\Filament\\Widgets\\RecentActivitiesWidget), '__lazyLoad', Array, Object(Livewire\\Mechanisms\\HandleComponents\\ComponentContext), Object(Closure))
#23 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(471): Livewire\\trigger('call', Object(App\\Filament\\Widgets\\RecentActivitiesWidget), '__lazyLoad', Array, Object(Livewire\\Mechanisms\\HandleComponents\\ComponentContext), Object(Closure))
#24 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(101): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->callMethods(Object(App\\Filament\\Widgets\\RecentActivitiesWidget), Array, Object(Livewire\\Mechanisms\\HandleComponents\\ComponentContext))
#25 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\livewire\\livewire\\src\\LivewireManager.php(102): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->update(Array, Array, Array)
#26 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleRequests\\HandleRequests.php(94): Livewire\\LivewireManager->update(Array, Array, Array)
#27 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): Livewire\\Mechanisms\\HandleRequests\\HandleRequests->handleUpdate()
#28 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(Livewire\\Mechanisms\\HandleRequests\\HandleRequests), 'handleUpdate')
#29 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#30 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(822): Illuminate\\Routing\\Route->run()
#31 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#40 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(821): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#47 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(800): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#48 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(764): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#49 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(753): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#50 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#51 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(47): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#59 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#61 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#62 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#63 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#64 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#65 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#66 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#67 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#68 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#69 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#70 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#71 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#72 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#73 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#74 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#75 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\laragon\\\\www\\\\...')
#76 {main}
"} 
[2025-08-08 14:54:10] local.ERROR: View [filament.widgets.recent-activities] not found. {"userId":1,"exception":"[object] (InvalidArgumentException(code: 0): View [filament.widgets.recent-activities] not found. at C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\View\\FileViewFinder.php:138)
[stacktrace]
#0 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\View\\FileViewFinder.php(78): Illuminate\\View\\FileViewFinder->findInPaths('filament.widget...', Array)
#1 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Factory.php(150): Illuminate\\View\\FileViewFinder->find('filament.widget...')
#2 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(1119): Illuminate\\View\\Factory->make('filament.widget...', Array, Array)
#3 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\filament\\widgets\\src\\Widget.php(73): view('filament.widget...', Array)
#4 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Filament\\Widgets\\Widget->render()
#5 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#6 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#7 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#8 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\livewire\\livewire\\src\\Wrapped.php(23): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array)
#9 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(289): Livewire\\Wrapped->__call('render', Array)
#10 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(249): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->getView(Object(App\\Filament\\Widgets\\RecentActivitiesWidget))
#11 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(104): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->render(Object(App\\Filament\\Widgets\\RecentActivitiesWidget))
#12 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\livewire\\livewire\\src\\LivewireManager.php(102): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->update(Array, Array, Array)
#13 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleRequests\\HandleRequests.php(94): Livewire\\LivewireManager->update(Array, Array, Array)
#14 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): Livewire\\Mechanisms\\HandleRequests\\HandleRequests->handleUpdate()
#15 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(Livewire\\Mechanisms\\HandleRequests\\HandleRequests), 'handleUpdate')
#16 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#17 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(822): Illuminate\\Routing\\Route->run()
#18 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#27 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(821): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#34 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(800): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#35 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(764): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#36 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(753): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#37 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#38 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(47): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#59 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#60 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#61 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#62 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\laragon\\\\www\\\\...')
#63 {main}
"} 
[2025-08-08 15:03:24] local.ERROR: Class "App\Filament\Resources\FinancialReportResource\Pages\BalanceSheet" not found {"exception":"[object] (Error(code: 0): Class \"App\\Filament\\Resources\\FinancialReportResource\\Pages\\BalanceSheet\" not found at C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\app\\Filament\\Resources\\FinancialReportResource.php:22)
[stacktrace]
#0 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\filament\\filament\\src\\Panel\\Concerns\\HasComponents.php(525): App\\Filament\\Resources\\FinancialReportResource::getPages()
#1 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\filament\\filament\\src\\Panel.php(69): Filament\\Panel->registerLivewireComponents()
#2 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\filament\\filament\\src\\PanelRegistry.php(19): Filament\\Panel->register()
#3 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\filament\\filament\\src\\Facades\\Filament.php(140): Filament\\PanelRegistry->register(Object(Filament\\Panel))
#4 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1596): Filament\\Facades\\Filament::Filament\\Facades\\{closure}(Object(Filament\\PanelRegistry), Object(Illuminate\\Foundation\\Application))
#5 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1514): Illuminate\\Container\\Container->fireCallbackArray(Object(Filament\\PanelRegistry), Array)
#6 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(951): Illuminate\\Container\\Container->fireResolvingCallbacks('Filament\\\\PanelR...', Object(Filament\\PanelRegistry))
#7 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('Filament\\\\PanelR...', Array, true)
#8 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(864): Illuminate\\Foundation\\Application->resolve('Filament\\\\PanelR...', Array)
#9 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('Filament\\\\PanelR...', Array)
#10 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(126): Illuminate\\Foundation\\Application->make('Filament\\\\PanelR...', Array)
#11 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\filament\\filament\\src\\FilamentManager.php(51): app('Filament\\\\PanelR...')
#12 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\filament\\filament\\src\\FilamentServiceProvider.php(48): Filament\\FilamentManager->__construct()
#13 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1111): Filament\\FilamentServiceProvider->Filament\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#14 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(933): Illuminate\\Container\\Container->build(Object(Closure))
#15 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('filament', Array, true)
#16 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(864): Illuminate\\Foundation\\Application->resolve('filament', Array)
#17 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('filament', Array)
#18 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1779): Illuminate\\Foundation\\Application->make('filament')
#19 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(239): Illuminate\\Container\\Container->offsetGet('filament')
#20 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(210): Illuminate\\Support\\Facades\\Facade::resolveFacadeInstance('filament')
#21 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Support\\Facades\\Facade::getFacadeRoot()
#22 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\filament\\filament\\routes\\web.php(13): Illuminate\\Support\\Facades\\Facade::__callStatic('getPanels', Array)
#23 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(524): Illuminate\\Support\\ServiceProvider->{closure}(Object(Illuminate\\Routing\\Router))
#24 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(480): Illuminate\\Routing\\Router->loadRoutes(Object(Closure))
#25 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteRegistrar.php(206): Illuminate\\Routing\\Router->group(Array, Object(Closure))
#26 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\filament\\filament\\routes\\web.php(12): Illuminate\\Routing\\RouteRegistrar->group(Object(Closure))
#27 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(194): require('C:\\\\laragon\\\\www\\\\...')
#28 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\PackageServiceProvider\\ProcessRoutes.php(14): Illuminate\\Support\\ServiceProvider->loadRoutesFrom('C:\\\\laragon\\\\www\\\\...')
#29 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\spatie\\laravel-package-tools\\src\\PackageServiceProvider.php(85): Spatie\\LaravelPackageTools\\PackageServiceProvider->bootPackageRoutes()
#30 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Spatie\\LaravelPackageTools\\PackageServiceProvider->boot()
#31 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#32 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#33 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#34 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(797): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#35 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1150): Illuminate\\Container\\Container->call(Array)
#36 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1131): Illuminate\\Foundation\\Application->bootProvider(Object(Filament\\FilamentServiceProvider))
#37 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(Filament\\FilamentServiceProvider), 'Filament\\\\Filame...')
#38 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1130): array_walk(Array, Object(Closure))
#39 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#40 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(341): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#41 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(186): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#42 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(170): Illuminate\\Foundation\\Http\\Kernel->bootstrap()
#43 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#44 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#45 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#46 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\laragon\\\\www\\\\...')
#47 {main}
"} 
[2025-08-08 15:03:29] local.ERROR: Class "App\Filament\Resources\FinancialReportResource\Pages\BalanceSheet" not found {"exception":"[object] (Error(code: 0): Class \"App\\Filament\\Resources\\FinancialReportResource\\Pages\\BalanceSheet\" not found at C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\app\\Filament\\Resources\\FinancialReportResource.php:22)
[stacktrace]
#0 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\filament\\filament\\src\\Panel\\Concerns\\HasComponents.php(525): App\\Filament\\Resources\\FinancialReportResource::getPages()
#1 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\filament\\filament\\src\\Panel.php(69): Filament\\Panel->registerLivewireComponents()
#2 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\filament\\filament\\src\\PanelRegistry.php(19): Filament\\Panel->register()
#3 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\filament\\filament\\src\\Facades\\Filament.php(140): Filament\\PanelRegistry->register(Object(Filament\\Panel))
#4 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1596): Filament\\Facades\\Filament::Filament\\Facades\\{closure}(Object(Filament\\PanelRegistry), Object(Illuminate\\Foundation\\Application))
#5 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1514): Illuminate\\Container\\Container->fireCallbackArray(Object(Filament\\PanelRegistry), Array)
#6 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(951): Illuminate\\Container\\Container->fireResolvingCallbacks('Filament\\\\PanelR...', Object(Filament\\PanelRegistry))
#7 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('Filament\\\\PanelR...', Array, true)
#8 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(864): Illuminate\\Foundation\\Application->resolve('Filament\\\\PanelR...', Array)
#9 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('Filament\\\\PanelR...', Array)
#10 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(126): Illuminate\\Foundation\\Application->make('Filament\\\\PanelR...', Array)
#11 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\filament\\filament\\src\\FilamentManager.php(51): app('Filament\\\\PanelR...')
#12 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\filament\\filament\\src\\FilamentServiceProvider.php(48): Filament\\FilamentManager->__construct()
#13 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1111): Filament\\FilamentServiceProvider->Filament\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#14 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(933): Illuminate\\Container\\Container->build(Object(Closure))
#15 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('filament', Array, true)
#16 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(864): Illuminate\\Foundation\\Application->resolve('filament', Array)
#17 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('filament', Array)
#18 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1779): Illuminate\\Foundation\\Application->make('filament')
#19 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(239): Illuminate\\Container\\Container->offsetGet('filament')
#20 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(210): Illuminate\\Support\\Facades\\Facade::resolveFacadeInstance('filament')
#21 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Support\\Facades\\Facade::getFacadeRoot()
#22 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\filament\\filament\\routes\\web.php(13): Illuminate\\Support\\Facades\\Facade::__callStatic('getPanels', Array)
#23 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(524): Illuminate\\Support\\ServiceProvider->{closure}(Object(Illuminate\\Routing\\Router))
#24 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(480): Illuminate\\Routing\\Router->loadRoutes(Object(Closure))
#25 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteRegistrar.php(206): Illuminate\\Routing\\Router->group(Array, Object(Closure))
#26 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\filament\\filament\\routes\\web.php(12): Illuminate\\Routing\\RouteRegistrar->group(Object(Closure))
#27 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(194): require('C:\\\\laragon\\\\www\\\\...')
#28 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\PackageServiceProvider\\ProcessRoutes.php(14): Illuminate\\Support\\ServiceProvider->loadRoutesFrom('C:\\\\laragon\\\\www\\\\...')
#29 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\spatie\\laravel-package-tools\\src\\PackageServiceProvider.php(85): Spatie\\LaravelPackageTools\\PackageServiceProvider->bootPackageRoutes()
#30 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Spatie\\LaravelPackageTools\\PackageServiceProvider->boot()
#31 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#32 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#33 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#34 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(797): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#35 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1150): Illuminate\\Container\\Container->call(Array)
#36 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1131): Illuminate\\Foundation\\Application->bootProvider(Object(Filament\\FilamentServiceProvider))
#37 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(Filament\\FilamentServiceProvider), 'Filament\\\\Filame...')
#38 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1130): array_walk(Array, Object(Closure))
#39 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#40 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(341): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#41 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(186): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#42 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(170): Illuminate\\Foundation\\Http\\Kernel->bootstrap()
#43 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#44 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#45 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#46 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\laragon\\\\www\\\\...')
#47 {main}
"} 
[2025-08-08 15:03:33] local.ERROR: Class "App\Filament\Resources\FinancialReportResource\Pages\BalanceSheet" not found {"exception":"[object] (Error(code: 0): Class \"App\\Filament\\Resources\\FinancialReportResource\\Pages\\BalanceSheet\" not found at C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\app\\Filament\\Resources\\FinancialReportResource.php:22)
[stacktrace]
#0 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\filament\\filament\\src\\Panel\\Concerns\\HasComponents.php(525): App\\Filament\\Resources\\FinancialReportResource::getPages()
#1 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\filament\\filament\\src\\Panel.php(69): Filament\\Panel->registerLivewireComponents()
#2 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\filament\\filament\\src\\PanelRegistry.php(19): Filament\\Panel->register()
#3 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\filament\\filament\\src\\Facades\\Filament.php(140): Filament\\PanelRegistry->register(Object(Filament\\Panel))
#4 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1596): Filament\\Facades\\Filament::Filament\\Facades\\{closure}(Object(Filament\\PanelRegistry), Object(Illuminate\\Foundation\\Application))
#5 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1514): Illuminate\\Container\\Container->fireCallbackArray(Object(Filament\\PanelRegistry), Array)
#6 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(951): Illuminate\\Container\\Container->fireResolvingCallbacks('Filament\\\\PanelR...', Object(Filament\\PanelRegistry))
#7 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('Filament\\\\PanelR...', Array, true)
#8 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(864): Illuminate\\Foundation\\Application->resolve('Filament\\\\PanelR...', Array)
#9 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('Filament\\\\PanelR...', Array)
#10 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(126): Illuminate\\Foundation\\Application->make('Filament\\\\PanelR...', Array)
#11 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\filament\\filament\\src\\FilamentManager.php(51): app('Filament\\\\PanelR...')
#12 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\filament\\filament\\src\\FilamentServiceProvider.php(48): Filament\\FilamentManager->__construct()
#13 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1111): Filament\\FilamentServiceProvider->Filament\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#14 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(933): Illuminate\\Container\\Container->build(Object(Closure))
#15 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('filament', Array, true)
#16 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(864): Illuminate\\Foundation\\Application->resolve('filament', Array)
#17 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('filament', Array)
#18 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1779): Illuminate\\Foundation\\Application->make('filament')
#19 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(239): Illuminate\\Container\\Container->offsetGet('filament')
#20 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(210): Illuminate\\Support\\Facades\\Facade::resolveFacadeInstance('filament')
#21 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Support\\Facades\\Facade::getFacadeRoot()
#22 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\filament\\filament\\routes\\web.php(13): Illuminate\\Support\\Facades\\Facade::__callStatic('getPanels', Array)
#23 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(524): Illuminate\\Support\\ServiceProvider->{closure}(Object(Illuminate\\Routing\\Router))
#24 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(480): Illuminate\\Routing\\Router->loadRoutes(Object(Closure))
#25 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteRegistrar.php(206): Illuminate\\Routing\\Router->group(Array, Object(Closure))
#26 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\filament\\filament\\routes\\web.php(12): Illuminate\\Routing\\RouteRegistrar->group(Object(Closure))
#27 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(194): require('C:\\\\laragon\\\\www\\\\...')
#28 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\PackageServiceProvider\\ProcessRoutes.php(14): Illuminate\\Support\\ServiceProvider->loadRoutesFrom('C:\\\\laragon\\\\www\\\\...')
#29 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\spatie\\laravel-package-tools\\src\\PackageServiceProvider.php(85): Spatie\\LaravelPackageTools\\PackageServiceProvider->bootPackageRoutes()
#30 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Spatie\\LaravelPackageTools\\PackageServiceProvider->boot()
#31 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#32 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#33 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#34 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(797): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#35 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1150): Illuminate\\Container\\Container->call(Array)
#36 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1131): Illuminate\\Foundation\\Application->bootProvider(Object(Filament\\FilamentServiceProvider))
#37 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(Filament\\FilamentServiceProvider), 'Filament\\\\Filame...')
#38 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1130): array_walk(Array, Object(Closure))
#39 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#40 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(341): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#41 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(186): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#42 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(170): Illuminate\\Foundation\\Http\\Kernel->bootstrap()
#43 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#44 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#45 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#46 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\laragon\\\\www\\\\...')
#47 {main}
"} 
[2025-08-08 15:03:36] local.ERROR: Class "App\Filament\Resources\FinancialReportResource\Pages\BalanceSheet" not found {"exception":"[object] (Error(code: 0): Class \"App\\Filament\\Resources\\FinancialReportResource\\Pages\\BalanceSheet\" not found at C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\app\\Filament\\Resources\\FinancialReportResource.php:22)
[stacktrace]
#0 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\filament\\filament\\src\\Panel\\Concerns\\HasComponents.php(525): App\\Filament\\Resources\\FinancialReportResource::getPages()
#1 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\filament\\filament\\src\\Panel.php(69): Filament\\Panel->registerLivewireComponents()
#2 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\filament\\filament\\src\\PanelRegistry.php(19): Filament\\Panel->register()
#3 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\filament\\filament\\src\\Facades\\Filament.php(140): Filament\\PanelRegistry->register(Object(Filament\\Panel))
#4 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1596): Filament\\Facades\\Filament::Filament\\Facades\\{closure}(Object(Filament\\PanelRegistry), Object(Illuminate\\Foundation\\Application))
#5 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1514): Illuminate\\Container\\Container->fireCallbackArray(Object(Filament\\PanelRegistry), Array)
#6 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(951): Illuminate\\Container\\Container->fireResolvingCallbacks('Filament\\\\PanelR...', Object(Filament\\PanelRegistry))
#7 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('Filament\\\\PanelR...', Array, true)
#8 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(864): Illuminate\\Foundation\\Application->resolve('Filament\\\\PanelR...', Array)
#9 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('Filament\\\\PanelR...', Array)
#10 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(126): Illuminate\\Foundation\\Application->make('Filament\\\\PanelR...', Array)
#11 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\filament\\filament\\src\\FilamentManager.php(51): app('Filament\\\\PanelR...')
#12 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\filament\\filament\\src\\FilamentServiceProvider.php(48): Filament\\FilamentManager->__construct()
#13 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1111): Filament\\FilamentServiceProvider->Filament\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#14 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(933): Illuminate\\Container\\Container->build(Object(Closure))
#15 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('filament', Array, true)
#16 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(864): Illuminate\\Foundation\\Application->resolve('filament', Array)
#17 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('filament', Array)
#18 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1779): Illuminate\\Foundation\\Application->make('filament')
#19 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(239): Illuminate\\Container\\Container->offsetGet('filament')
#20 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(210): Illuminate\\Support\\Facades\\Facade::resolveFacadeInstance('filament')
#21 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Support\\Facades\\Facade::getFacadeRoot()
#22 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\filament\\filament\\routes\\web.php(13): Illuminate\\Support\\Facades\\Facade::__callStatic('getPanels', Array)
#23 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(524): Illuminate\\Support\\ServiceProvider->{closure}(Object(Illuminate\\Routing\\Router))
#24 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(480): Illuminate\\Routing\\Router->loadRoutes(Object(Closure))
#25 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteRegistrar.php(206): Illuminate\\Routing\\Router->group(Array, Object(Closure))
#26 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\filament\\filament\\routes\\web.php(12): Illuminate\\Routing\\RouteRegistrar->group(Object(Closure))
#27 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(194): require('C:\\\\laragon\\\\www\\\\...')
#28 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\PackageServiceProvider\\ProcessRoutes.php(14): Illuminate\\Support\\ServiceProvider->loadRoutesFrom('C:\\\\laragon\\\\www\\\\...')
#29 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\spatie\\laravel-package-tools\\src\\PackageServiceProvider.php(85): Spatie\\LaravelPackageTools\\PackageServiceProvider->bootPackageRoutes()
#30 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Spatie\\LaravelPackageTools\\PackageServiceProvider->boot()
#31 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#32 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#33 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#34 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(797): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#35 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1150): Illuminate\\Container\\Container->call(Array)
#36 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1131): Illuminate\\Foundation\\Application->bootProvider(Object(Filament\\FilamentServiceProvider))
#37 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(Filament\\FilamentServiceProvider), 'Filament\\\\Filame...')
#38 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1130): array_walk(Array, Object(Closure))
#39 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#40 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(341): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#41 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(186): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#42 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(170): Illuminate\\Foundation\\Http\\Kernel->bootstrap()
#43 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#44 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#45 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#46 C:\\laragon\\www\\WB-PROJEK\\WB-ACOUNTING-V1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\laragon\\\\www\\\\...')
#47 {main}
"} 
