<?php

namespace App\Filament\Widgets;

use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;

class ProfitWidget extends BaseWidget
{
    protected static ?int $sort = 4;

    protected function getStats(): array
    {
        return [
            Stat::make('Kas & Bank', 'Rp 95.800.000')
                ->description('Total saldo kas dan bank')
                ->descriptionIcon('heroicon-m-building-library')
                ->color('primary')
                ->chart([95, 92, 88, 90, 94, 96, 95]),
        ];
    }
}
